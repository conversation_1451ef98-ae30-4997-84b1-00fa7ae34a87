#!/bin/bash
# 
# اسکریپت نصب و تست تشخیص‌گر Team Bye
# 

echo "🚀 نصب تشخیص‌گر Team Bye"
echo "============================="

# بررسی وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 نصب نشده است"
    exit 1
fi

echo "✅ Python3 موجود است"

# بررسی وجود pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 نصب نشده است"
    exit 1
fi

echo "✅ pip3 موجود است"

# بررسی وجود مدل
MODEL_PATH="/home/<USER>/Desktop/main/projects/openWakeWord/team_bye.tflite"
if [ ! -f "$MODEL_PATH" ]; then
    echo "❌ فایل مدل یافت نشد: $MODEL_PATH"
    echo "   لطفاً مدل team_bye.tflite را در مسیر بالا قرار دهید"
    exit 1
fi

echo "✅ فایل مدل موجود است"

# نصب پیش‌نیازها
echo ""
echo "📦 نصب پیش‌نیازها..."
pip3 install -r requirements_team_bye.txt

if [ $? -ne 0 ]; then
    echo "❌ خطا در نصب پیش‌نیازها"
    echo "لطفاً به صورت دستی نصب کنید:"
    echo "pip3 install openwakeword PyAudio numpy"
    exit 1
fi

echo "✅ پیش‌نیازها نصب شدند"

# تست سیستم
echo ""
echo "🧪 تست سیستم..."
python3 test_team_bye.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 نصب و تست موفق بود!"
    echo ""
    echo "برای اجرای تشخیص‌گر:"
    echo "python3 team_bye_detector.py"
    echo ""
    echo "برای تنظیم آستانه:"
    echo "python3 team_bye_detector.py --threshold 0.3"
    echo ""
    echo "برای انتخاب میکروفون:"
    echo "python3 team_bye_detector.py --list-devices"
    echo "python3 team_bye_detector.py --device-id X"
else
    echo "❌ تست ناموفق بود. لطفاً خطاها را بررسی کنید."
    exit 1
fi