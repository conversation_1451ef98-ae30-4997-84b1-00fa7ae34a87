# تشخیص‌گر Team Bye

این اسکریپت برای تشخیص کلمه "Team Bye" با استفاده از openWakeWord و مدل سفارشی .tflite طراحی شده است.

## فایل‌ها

- `team_bye_detector.py` - اسکریپت اصلی تشخیص‌گر
- `test_team_bye.py` - اسکریپت تست و بررسی سیستم  
- `requirements_team_bye.txt` - پیش‌نیازهای لازم
- `team_bye.tflite` - مدل TensorFlow Lite

## مسیر مدل

```
/home/<USER>/Desktop/main/projects/openWakeWord/team_bye.tflite
```

## نصب پیش‌نیازها

```bash
# نصب پیش‌نیازها
pip install -r requirements_team_bye.txt

# یا به صورت دستی
pip install openwakeword PyAudio numpy
```

## تست سیستم

قبل از استفاده، سیستم را تست کنید:

```bash
python test_team_bye.py
```

این دستور:
- اطلاعات مدل را نمایش می‌دهد
- دستگاه‌های صوتی موجود را لیست می‌کند
- تست اجرای مدل انجام می‌دهد

## استفاده پایه

```bash
# اجرای ساده
python team_bye_detector.py

# نمایش دستگاه‌های صوتی
python team_bye_detector.py --list-devices

# تنظیم آستانه تشخیص (پایین‌تر = حساسیت بیشتر)
python team_bye_detector.py --threshold 0.3

# انتخاب میکروفون خاص
python team_bye_detector.py --device-id 1
```

## پارامترهای پیشرفته

```bash
# مسیر مدل سفارشی
python team_bye_detector.py --model-path "/path/to/your/model.tflite"

# تنظیم اندازه فریم صوتی
python team_bye_detector.py --chunk-size 1600

# زمان Cooldown بعد از تشخیص
python team_bye_detector.py --cooldown 3.0

# اندازه پنجره هموارسازی
python team_bye_detector.py --smoothing 5
```

## مثال کامل

```bash
python team_bye_detector.py \
  --threshold 0.4 \
  --device-id 0 \
  --cooldown 2.5 \
  --smoothing 4
```

## نکات مهم

1. **آستانه تشخیص**: 
   - `0.1-0.3`: حساسیت بالا (ممکن است تشخیص اشتباه داشته باشد)
   - `0.4-0.6`: حساسیت متعادل (پیشنهادی)
   - `0.7-0.9`: حساسیت پایین (فقط تشخیص‌های قطعی)

2. **مسائل رایج**:
   - اگر مدل یافت نشد، مسیر فایل `team_bye.tflite` را بررسی کنید
   - اگر صدا دریافت نمی‌شود، دستگاه‌های صوتی را با `--list-devices` بررسی کنید
   - اگر openWakeWord خطا داد، با `pip install openwakeword` نصب کنید

3. **بهینه‌سازی عملکرد**:
   - از `chunk_size=1280` (80ms) برای تأخیر کم استفاده کنید
   - `smoothing_window=3-5` برای کاهش نویز مناسب است
   - `cooldown_time=2-3` ثانیه از تشخیص‌های مکرر جلوگیری می‌کند

## خروج از برنامه

برای خروج از برنامه `Ctrl+C` فشار دهید.

## عیب‌یابی

اگر مشکلی داشتید:

1. ابتدا `test_team_bye.py` را اجرا کنید
2. بررسی کنید openWakeWord نصب باشد: `pip list | grep -i openwakeword`
3. بررسی کنید PyAudio نصب باشد: `pip list | grep -i pyaudio`
4. مسیر مدل را تأیید کنید که وجود دارد
5. میکروفون دستگاه را تست کنید

## نمونه خروجی

```
==========================================
👋 شروع تشخیص کلمه 'TEAM BYE'
📁 مسیر مدل: /path/to/team_bye.tflite  
🎯 آستانه: 0.5
⏱️  زمان Cooldown: 2.0 ثانیه
📊 پنجره هموارسازی: 3
   برای خروج Ctrl+C را فشار دهید
==========================================

👋 [2024-01-15 10:30:45.123] 'TEAM BYE' تشخیص داده شد!
   📊 نمره خام: 0.8234
   📈 نمره هموار: 0.7891  
   🎯 آستانه: 0.5
   ============================================================
```