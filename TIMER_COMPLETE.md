# 🎯 تشخیص دستورات Timer - راهکار کامل

## 🚀 راه‌اندازی فوری (30 ثانیه)

```bash
# فعال‌سازی محیط مجازی
source .venv/bin/activate

# راه‌اندازی خودکار
./setup_timer.sh

# اجرای فوری
python timer_detector.py
```

## 📁 فایل‌های پروژه

| فایل | کاربرد |
|------|--------|
| `timer_detector.py` | ⭐ **برنامه اصلی** - تشخیص کننده timer |
| `download_models.py` | دانلود مدل timer |
| `test_timer.py` | تست بدون نیاز به صحبت |
| `setup_timer.sh` | راه‌اندازی خودکار |
| `requirements_timer.txt` | وابستگی‌های لازم |
| `README_TIMER.md` | 📖 راهنمای کامل |

## ⚡ دستورات اصلی

```bash
# اجرای عادی (آستانه 0.3)
python timer_detector.py

# حساسیت بالا (تشخیص بیشتر)
python timer_detector.py --threshold 0.2

# محیط پرسروصدا (تشخیص دقیق‌تر)
python timer_detector.py --threshold 0.5 --smoothing 7

# با PulseAudio
python timer_detector.py --device-id 13

# مشاهده دستگاه‌های صوتی
python timer_detector.py --list-devices
```

## 🗣️ عبارات تست

| مدت | عبارت انگلیسی |
|------|---------------|
| 1 دقیقه | `"set a timer for 1 minute"` |
| 5 دقیقه | `"set a timer for 5 minutes"` |
| 10 دقیقه | `"start 10 minute timer"` |
| 20 دقیقه | `"set a timer for 20 minutes"` |
| 30 دقیقه | `"start 30 minute timer"` |
| 1 ساعت | `"set a timer for 1 hour"` |

## 📊 نمونه خروجی

```
⏰ [2025-08-08 12:30:15.123] دستور تایمر تشخیص داده شد!
   ======================================================================
   🎯 #1: تایمر 5 دقیقه
      📊 نمره خام: 0.7845
      📈 نمره هموار: 0.7234
      🔥 اعتماد: بالا
   ======================================================================
```

## 🔧 تنظیمات سریع

### محیط‌های مختلف
```bash
# خانه (خاموش)
python timer_detector.py --threshold 0.25

# آشپزخانه (پرسروصدا) 
python timer_detector.py --threshold 0.45 --smoothing 6

# ورزش (حرکت)
python timer_detector.py --threshold 0.4 --cooldown 2

# دفتر (معمولی)
python timer_detector.py --threshold 0.35 --cooldown 3
```

### عیب‌یابی سریع
```bash
# تشخیص نمی‌کند → آستانه را کم کنید
python timer_detector.py --threshold 0.15

# تشخیص اشتباه زیاد → آستانه را زیاد کنید  
python timer_detector.py --threshold 0.6

# خطای NumPy
pip install "numpy<2.0"

# مدل موجود نیست
python download_models.py
```

## 🎯 کلاس‌های timer

1. **1_minute_timer** - تایمر 1 دقیقه
2. **5_minute_timer** - تایمر 5 دقیقه  
3. **10_minute_timer** - تایمر 10 دقیقه
4. **20_minute_timer** - تایمر 20 دقیقه
5. **30_minute_timer** - تایمر 30 دقیقه
6. **1_hour_timer** - تایمر 1 ساعت

## ✅ ویژگی‌های پیاده‌سازی شده

- ✅ **Multi-class Detection** - تشخیص همزمان 6 نوع تایمر
- ✅ **Smoothing Algorithm** - هموارسازی نمره‌ها
- ✅ **Cooldown System** - جلوگیری از تکرار
- ✅ **Confidence Levels** - سطوح اعتماد (بالا/متوسط/پایین)
- ✅ **Audio Device Selection** - انتخاب میکروفون
- ✅ **Real-time Processing** - پردازش زنده
- ✅ **Cross-platform** - Linux/macOS/Windows
- ✅ **Colored Output** - خروجی رنگی
- ✅ **Signal Handling** - خروج تمیز با Ctrl+C
- ✅ **Error Handling** - مدیریت خطاهای شایع
- ✅ **Auto Model Download** - دانلود خودکار مدل
- ✅ **Configurable Parameters** - پارامترهای قابل تنظیم

## 📈 عملکرد و بهینه‌سازی

### تنظیمات عملکرد
```python
# کم‌ترین تأخیر
chunk_size = 800       # 50ms فریم
smoothing = 1          # بدون هموارسازی

# بالاترین دقت
chunk_size = 1280      # 80ms فریم (استاندارد)
smoothing = 7          # هموارسازی بیشتر
threshold = 0.5        # آستانه محافظه‌کارانه

# متعادل (پیشنهادی)
chunk_size = 1280      # 80ms
smoothing = 5          # متعادل  
threshold = 0.3        # حساسیت مناسب
cooldown = 3.0         # انتظار مناسب
```

## 🏁 نتیجه‌گیری

### ✅ آماده برای استفاده در:
- 🍳 **آشپزی** - تایمرهای پخت
- 💪 **ورزش** - تایمر استراحت و تمرین
- 📚 **مطالعه** - تکنیک Pomodoro
- ⏰ **کارهای عمومی** - یادآوری‌ها

### 🎯 نقاط قوت:
- **تشخیص آفلاین** - بدون نیاز به اینترنت
- **CPU مبنا** - بدون نیاز به GPU
- **تنظیمات انعطاف‌پذیر** - برای محیط‌های مختلف
- **خروجی کاربردی** - لاگ واضح و مفید
- **پیاده‌سازی کامل** - تمام ویژگی‌های لازم

### 🚀 آماده استفاده!

```bash
source .venv/bin/activate
python timer_detector.py --threshold 0.3
# سپس بگویید: "set a timer for 5 minutes"
```

---

**تاریخ آخرین بروزرسانی**: 2025-08-08  
**نسخه openWakeWord**: v0.6.0  
**وضعیت**: ✅ آزمایش شده و آماده استفاده