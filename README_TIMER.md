# 🎯 تشخیص دستورات تایمر با openWakeWord

## 📋 فهرست مطالب
- [نصب](#نصب)
- [اجرای سریع](#اجرای-سریع)
- [عبارات قابل تشخیص](#عبارات-قابل-تشخیص)
- [راهنمای تنظیمات](#راهنمای-تنظیمات)
- [تست و عیب‌یابی](#تست-و-عیب‌یابی)
- [نمونه خروجی](#نمونه-خروجی)

## 🚀 نصب

### 1. نصب وابستگی‌ها
```bash
# فعال‌سازی محیط مجازی
source .venv/bin/activate

# نصب numpy سازگار
pip install "numpy<2.0"

# بررسی نصب 
pip list | grep -E "(openwakeword|numpy|pyaudio)"
```

### 2. دانلود مدل timer
```bash
python download_models.py
```

## ⚡ اجرای سریع

### اجرای عادی
```bash
python timer_detector.py
```

### با تنظیمات سفارشی
```bash
# حساسیت بالا (آستانه پایین)
python timer_detector.py --threshold 0.2

# برای محیط پرسروصدا
python timer_detector.py --threshold 0.5 --smoothing 7

# با دستگاه مشخص
python timer_detector.py --device-id 13
```

## 🗣️ عبارات قابل تشخیس

### ✅ عبارات مؤثر

| مدت زمان | عبارات نمونه |
|-----------|---------------|
| **1 دقیقه** | "set a timer for one minute" |
| | "start 1 minute timer" |
| | "timer for one minute" |
| **5 دقیقه** | "set a timer for 5 minutes" |
| | "start five minute timer" |
| | "set an alarm for 5 minutes" |
| **10 دقیقه** | "set a timer for 10 minutes" |
| | "start ten minute timer" |
| | "timer for 10 minutes" |
| **20 دقیقه** | "set a timer for 20 minutes" |
| | "start twenty minute timer" |
| **30 دقیقه** | "set a timer for 30 minutes" |
| | "start thirty minute timer" |
| | "set an alarm for half hour" |
| **1 ساعت** | "set a timer for 1 hour" |
| | "start one hour timer" |
| | "set an alarm for one hour" |

### 💡 نکات مهم
- کلمات کلیدی: `timer`, `alarm`, `set`, `start`
- اعداد قابل تشخیص: `1`, `5`, `10`, `20`, `30` دقیقه و `1` ساعت
- هر عبارت شامل مدت زمان مشخص باشد
- صدا واضح و بدون عجله بگویید

## ⚙️ راهنمای تنظیمات

### پارامترهای اصلی

```bash
python timer_detector.py [OPTIONS]
```

| پارامتر | پیش‌فرض | توضیح |
|---------|---------|-------|
| `--threshold` / `-t` | 0.3 | آستانه تشخیص (0.0-1.0) |
| `--device-id` / `-d` | None | شناسه دستگاه میکروفون |
| `--cooldown` / `-cd` | 3.0 | زمان انتظار بعد از تشخیص (ثانیه) |
| `--smoothing` / `-s` | 5 | اندازه پنجره هموارسازی |
| `--chunk-size` / `-c` | 1280 | اندازه فریم صوتی |

### 🎛️ تنظیمات توصیه شده

#### محیط خاموش
```bash
python timer_detector.py --threshold 0.25 --cooldown 2.0
```

#### محیط پرسروصدا
```bash
python timer_detector.py --threshold 0.5 --smoothing 7 --cooldown 4.0
```

#### حداکثر حساسیت
```bash
python timer_detector.py --threshold 0.15 --smoothing 3
```

#### حداقل False Positive
```bash
python timer_detector.py --threshold 0.6 --smoothing 10
```

## 🧪 تست و عیب‌یابی

### 1. تست بدون صحبت
```bash
python test_timer.py
```

### 2. مشاهده دستگاه‌های صوتی
```bash
python timer_detector.py --list-devices
```

### 3. تست با حساسیت بالا
```bash
timeout 30 python timer_detector.py --threshold 0.2
```

### مشکلات متداول

#### ❌ مشکل: "مدل timer یافت نشد"
```bash
python download_models.py
```

#### ❌ مشکل: خطای NumPy
```bash
pip install "numpy<2.0"
```

#### ❌ مشکل: تشخیص نمی‌کند
- آستانه را کاهش دهید: `--threshold 0.2`
- بلندتر صحبت کنید
- نزدیک‌تر به میکروفون باشید
- عبارات استاندارد استفاده کنید

#### ❌ مشکل: تشخیص اشتباه زیاد
- آستانه را افزایش دهید: `--threshold 0.5`
- هموارسازی بیشتر: `--smoothing 8`
- Cooldown طولانی‌تر: `--cooldown 5.0`

## 🎬 نمونه خروجی

### تشخیص موفق
```
================================================================================
⏰ شروع تشخیص دستورات تایمر
🎯 آستانه: 0.3
⏱️ زمان Cooldown: 3.0 ثانیه
📊 پنجره هموارسازی: 5

🗣️ عبارات قابل تشخیص:
   • 'set a timer for X minutes'
   • 'start X minute timer'
   • 'set an alarm for X minutes/hours'
   • 'timer for X minutes'

   برای خروج Ctrl+C را فشار دهید
================================================================================

⏰ [2025-08-08 12:25:34.567] دستور تایمر تشخیص داده شد!
   ======================================================================
   🎯 #1: تایمر 5 دقیقه
      📊 نمره خام: 0.7845
      📈 نمره هموار: 0.7234
      🔥 اعتماد: بالا
   ======================================================================
```

### تشخیص چندگانه
```
⏰ [2025-08-08 12:26:15.123] دستور تایمر تشخیص داده شد!
   ======================================================================
   🎯 #1: تایمر 10 دقیقه
      📊 نمره خام: 0.6234
      📈 نمره هموار: 0.5891
      🔥 اعتماد: متوسط
      ────────────────────
   🎯 #2: تایمر 5 دقیقه
      📊 نمره خام: 0.4567
      📈 نمره هموار: 0.4123
      🔥 اعتماد: متوسط
   ======================================================================
```

## 📚 مثال‌های عملی

### 1. استفاده در آشپزی
```bash
# تنظیم برای آشپزخانه (محیط پرسروصدا)
python timer_detector.py --threshold 0.4 --cooldown 4.0

# عبارات مفید:
# "set a timer for 5 minutes" (برای جوشاندن تخم‌مرغ)
# "start 10 minute timer" (برای پخت برنج)
# "set a timer for 30 minutes" (برای پخت کیک)
```

### 2. استفاده در ورزش
```bash
# تنظیم برای باشگاه
python timer_detector.py --threshold 0.3 --cooldown 2.0

# عبارات مفید:
# "start 1 minute timer" (استراحت بین ست‌ها)
# "set a timer for 20 minutes" (کاردیو)
# "start 30 minute timer" (ورزش کامل)
```

### 3. استفاده در مطالعه (تکنیک Pomodoro)
```bash
# تنظیم برای مطالعه
python timer_detector.py --threshold 0.35 --cooldown 3.0

# عبارات مفید:
# "set a timer for 25 minutes" (جلسه مطالعه)
# "start 5 minute timer" (استراحت کوتاه)
# "set a timer for 30 minutes" (استراحت بلند)
```

## 🔧 تنظیمات پیشرفته

### تغییر نرخ نمونه‌برداری
```python
# در فایل timer_detector.py
sample_rate: int = 16000  # نرخ استاندارد - تغییر نکنید
```

### تنظیم چندین آستانه
```python
# آستانه‌های مختلف برای کلاس‌های مختلف
class_thresholds = {
    "1_minute_timer": 0.25,   # حساسیت بالا برای تایمر کوتاه
    "5_minute_timer": 0.3,
    "10_minute_timer": 0.35,
    "20_minute_timer": 0.4,
    "30_minute_timer": 0.45,
    "1_hour_timer": 0.5       # آستانه بالاتر برای تایمر طولانی
}
```

## 📖 منابع و مراجع

- **مستندات رسمی**: [OpenWakeWord GitHub](https://github.com/dscripka/openWakeWord)
- **مدل Timer**: [Timer Model Documentation](https://github.com/dscripka/openWakeWord/blob/main/docs/models/timers.md)
- **PyPI Package**: [openwakeword](https://pypi.org/project/openwakeword/)
- **مسائل و پشتیبانی**: [GitHub Issues](https://github.com/dscripka/openWakeWord/issues)

## 🏁 خلاصه

این برنامه به‌طور کامل برای تشخیص دستورات تایمر بهینه‌سازی شده و شامل:

- ✅ **6 کلاس مختلف تایمر** (1, 5, 10, 20, 30 دقیقه و 1 ساعت)
- ✅ **تنظیمات قابل تنظیم** برای محیط‌های مختلف
- ✅ **هموارسازی و Cooldown** برای کاهش خطا
- ✅ **خروجی رنگی و زیبا** برای نمایش نتایج
- ✅ **راهنمای کامل** برای استفاده و عیب‌یابی
- ✅ **تست خودکار** بدون نیاز به صحبت

**آماده استفاده است!** 🚀