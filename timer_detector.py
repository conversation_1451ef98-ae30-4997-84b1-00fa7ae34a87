#!/usr/bin/env python3
"""
تشخیص دستورات تایمر با استفاده از openWakeWord
نویسنده: پیاده‌سازی مخصوص مدل timer
عبارات قابل تشخیص:
- "set a timer for X minutes"
- "start X minute timer"
- "set alarm for X hours"
"""

import argparse
import datetime
import logging
import signal
import sys
import time
from typing import Dict, Optional, List

import numpy as np
import pyaudio
from openwakeword.model import Model
import openwakeword


class TimerWakeWordDetector:
    """کلاس اصلی تشخیص دستورات تایمر"""
    
    # نقشه کلاس‌های timer (از openwakeword/__init__.py)
    TIMER_CLASSES = {
        "1": "1_minute_timer",
        "2": "5_minute_timer",
        "3": "10_minute_timer", 
        "4": "20_minute_timer",
        "5": "30_minute_timer",
        "6": "1_hour_timer"
    }
    
    TIMER_DESCRIPTIONS = {
        "1": "1 دقیقه",
        "2": "5 دقیقه", 
        "3": "10 دقیقه",
        "4": "20 دقیقه",
        "5": "30 دقیقه",
        "6": "1 ساعت"
    }
    
    def __init__(self, 
                 chunk_size: int = 1280,
                 threshold: float = 0.3,  # آستانه پایین‌تر برای timer
                 sample_rate: int = 16000,
                 channels: int = 1,
                 device_id: Optional[int] = None,
                 cooldown_time: float = 3.0,  # cooldown طولانی‌تر برای timer
                 smoothing_window: int = 5):  # smoothing بیشتر برای timer
        """
        مقداردهی تشخیص‌گر تایمر
        
        Args:
            chunk_size: اندازه فریم صوتی
            threshold: آستانه تشخیص (پایین‌تر برای timer)
            sample_rate: نرخ نمونه‌برداری
            channels: تعداد کانال‌ها
            device_id: شناسه دستگاه میکروفون
            cooldown_time: زمان انتظار بعد از تشخیص
            smoothing_window: اندازه پنجره هموارسازی
        """
        self.chunk_size = chunk_size
        self.threshold = threshold
        self.sample_rate = sample_rate
        self.channels = channels
        self.device_id = device_id
        self.cooldown_time = cooldown_time
        self.smoothing_window = smoothing_window
        
        # متغیرهای داخلی
        self.audio = None
        self.mic_stream = None
        self.oww_model = None
        self.last_detection_time = 0
        self.recent_scores = {cls: [] for cls in self.TIMER_CLASSES.values()}
        self.is_running = False
        
        # تنظیم logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def list_audio_devices(self):
        """فهرست دستگاه‌های صوتی موجود"""
        audio = pyaudio.PyAudio()
        print("\n=== دستگاه‌های صوتی موجود ===")
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:  # فقط دستگاه‌های ورودی
                print(f"ID {i}: {info['name']} - {info['maxInputChannels']} کانال - {info['defaultSampleRate']} Hz")
        audio.terminate()
        print("=====================================\n")

    def initialize_audio(self):
        """مقداردهی سیستم صوتی"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # بررسی دستگاه انتخاب شده
            if self.device_id is not None:
                device_info = self.audio.get_device_info_by_index(self.device_id)
                self.logger.info(f"استفاده از دستگاه: {device_info['name']}")
            
            # باز کردن جریان میکروفون
            self.mic_stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.device_id,
                frames_per_buffer=self.chunk_size
            )
            
            self.logger.info(f"جریان صوتی باز شد: {self.sample_rate}Hz، {self.chunk_size} فریم")
            return True
            
        except Exception as e:
            self.logger.error(f"خطا در مقداردهی صوت: {e}")
            return False

    def initialize_model(self):
        """بارگذاری مدل timer"""
        try:
            # تلاش برای بارگذاری مدل
            try:
                self.oww_model = Model(inference_framework='tflite')
            except Exception as model_error:
                self.logger.warning(f"خطا در بارگذاری مدل: {model_error}")
                self.logger.info("تلاش برای دانلود مدل‌های مورد نیاز...")
                
                # دانلود مدل timer
                from openwakeword.utils import download_models
                download_models(model_names=["timer"])
                
                # تلاش مجدد برای بارگذاری
                self.oww_model = Model(inference_framework='tflite')
            
            # بررسی وجود مدل timer
            available_models = list(self.oww_model.models.keys())
            self.logger.info(f"مدل‌های موجود: {available_models}")
            
            if 'timer' not in available_models:
                raise ValueError("مدل 'timer' یافت نشد")
            
            self.logger.info("مدل 'timer' بارگذاری شد")
            self.logger.info(f"کلاس‌های timer: {list(self.TIMER_DESCRIPTIONS.values())}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطا در بارگذاری مدل: {e}")
            return False

    def smooth_score(self, timer_class: str, score: float) -> float:
        """هموارسازی نمره‌ها برای کاهش نویز"""
        if timer_class not in self.recent_scores:
            self.recent_scores[timer_class] = []
            
        self.recent_scores[timer_class].append(score)
        
        # نگهداشتن فقط چند نمره اخیر
        if len(self.recent_scores[timer_class]) > self.smoothing_window:
            self.recent_scores[timer_class].pop(0)
        
        # محاسبه میانگین موزون (وزن بیشتر به نمره‌های جدیدتر)
        weights = np.linspace(0.3, 1.0, len(self.recent_scores[timer_class]))
        weighted_avg = np.average(self.recent_scores[timer_class], weights=weights)
        
        return weighted_avg

    def is_in_cooldown(self) -> bool:
        """بررسی وضعیت cooldown"""
        current_time = time.time()
        return (current_time - self.last_detection_time) < self.cooldown_time

    def detect_timer_commands(self) -> Optional[List[Dict]]:
        """تشخیص دستورات تایمر در یک فریم"""
        try:
            # دریافت داده صوتی
            audio_data = self.mic_stream.read(
                self.chunk_size, 
                exception_on_overflow=False
            )
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # پیش‌بینی با مدل
            predictions = self.oww_model.predict(audio_array)
            
            # بررسی cooldown
            if self.is_in_cooldown():
                return None
                
            # بررسی همه کلاس‌های timer
            detections = []
            timer_prediction_buffer = self.oww_model.prediction_buffer.get('timer', {})
            
            # timer یک multi-class model است، باید از prediction buffer استفاده کنیم
            if timer_prediction_buffer:
                latest_predictions = {k: v[-1] if v else 0.0 for k, v in timer_prediction_buffer.items()}
                
                for class_id, class_name in self.TIMER_CLASSES.items():
                    if class_name in latest_predictions:
                        raw_score = latest_predictions[class_name]
                        smooth_score = self.smooth_score(class_name, raw_score)
                        
                        if smooth_score >= self.threshold:
                            detections.append({
                                'class_id': class_id,
                                'class_name': class_name,
                                'description': self.TIMER_DESCRIPTIONS[class_id],
                                'raw_score': raw_score,
                                'smooth_score': smooth_score,
                                'threshold': self.threshold,
                                'timestamp': datetime.datetime.now()
                            })
            
            if detections:
                self.last_detection_time = time.time()
                return detections
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطا در تشخیص: {e}")
            return None

    def print_detection_log(self, detections: List[Dict]):
        """چاپ لاگ تشخیص تایمرها"""
        timestamp = detections[0]['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # چاپ با رنگ و ایموجی
        try:
            print(f"\033[94m⏰ [{timestamp}] دستور تایمر تشخیص داده شد!\033[0m")
            print(f"   {'='*70}")
            
            # مرتب‌سازی بر اساس نمره
            detections.sort(key=lambda x: x['smooth_score'], reverse=True)
            
            for i, detection in enumerate(detections):
                description = detection['description']
                raw_score = detection['raw_score']
                smooth_score = detection['smooth_score']
                
                confidence = "بالا" if smooth_score > 0.7 else "متوسط" if smooth_score > 0.5 else "پایین"
                
                print(f"   🎯 #{i+1}: تایمر {description}")
                print(f"      📊 نمره خام: {raw_score:.4f}")
                print(f"      📈 نمره هموار: {smooth_score:.4f}")
                print(f"      🔥 اعتماد: {confidence}")
                
                if i < len(detections) - 1:
                    print(f"      ────────────────────")
                    
            print(f"   {'='*70}")
            
        except:
            # fallback برای ترمینال‌هایی که رنگ را پشتیبانی نمی‌کنند
            print(f"[{timestamp}] دستور تایمر تشخیص داده شد!")
            for detection in detections:
                print(f"   {detection['description']}: {detection['smooth_score']:.4f}")

    def signal_handler(self, signum, frame):
        """مدیریت سیگنال‌های سیستم (Ctrl+C)"""
        self.logger.info("\nدریافت سیگنال خروج...")
        self.is_running = False

    def cleanup(self):
        """پاک‌سازی منابع"""
        if self.mic_stream:
            self.mic_stream.stop_stream()
            self.mic_stream.close()
            self.logger.info("جریان میکروفون بسته شد")
        
        if self.audio:
            self.audio.terminate()
            self.logger.info("سیستم صوتی بسته شد")

    def run(self):
        """اجرای حلقه اصلی تشخیص"""
        # تنظیم signal handler
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # مقداردهی سیستم‌ها
        if not self.initialize_audio():
            return False
        
        if not self.initialize_model():
            self.cleanup()
            return False
        
        # شروع تشخیص
        self.is_running = True
        self.logger.info("=" * 80)
        self.logger.info("⏰ شروع تشخیص دستورات تایمر")
        self.logger.info(f"🎯 آستانه: {self.threshold}")
        self.logger.info(f"⏱️  زمان Cooldown: {self.cooldown_time} ثانیه")
        self.logger.info(f"📊 پنجره هموارسازی: {self.smoothing_window}")
        self.logger.info("")
        self.logger.info("🗣️  عبارات قابل تشخیص:")
        self.logger.info("   • 'set a timer for X minutes'")
        self.logger.info("   • 'start X minute timer'")  
        self.logger.info("   • 'set an alarm for X minutes/hours'")
        self.logger.info("   • 'timer for X minutes'")
        self.logger.info("")
        self.logger.info("   برای خروج Ctrl+C را فشار دهید")
        self.logger.info("=" * 80)
        
        try:
            while self.is_running:
                detections = self.detect_timer_commands()
                if detections:
                    self.print_detection_log(detections)
                
                # استراحت کوتاه برای جلوگیری از استفاده بالای CPU
                time.sleep(0.01)
                
        except KeyboardInterrupt:
            self.logger.info("\nخروج با Ctrl+C...")
        except Exception as e:
            self.logger.error(f"خطای غیرمنتظره: {e}")
        finally:
            self.cleanup()
            self.logger.info("برنامه به پایان رسید")
        
        return True


def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(
        description="تشخیص دستورات تایمر با openWakeWord",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--threshold", "-t",
        type=float,
        default=0.3,
        help="آستانه تشخیص (0.0-1.0، پایین‌تر = حساسیت بیشتر)"
    )
    
    parser.add_argument(
        "--device-id", "-d",
        type=int,
        default=None,
        help="شناسه دستگاه میکروفون (برای مشاهده فهرست: --list-devices)"
    )
    
    parser.add_argument(
        "--list-devices", "-l",
        action="store_true",
        help="نمایش فهرست دستگاه‌های صوتی"
    )
    
    parser.add_argument(
        "--chunk-size", "-c",
        type=int,
        default=1280,
        help="اندازه فریم صوتی (نمونه، 1280 = 80ms در 16kHz)"
    )
    
    parser.add_argument(
        "--cooldown", "-cd",
        type=float,
        default=3.0,
        help="زمان انتظار بعد از هر تشخیص (ثانیه)"
    )
    
    parser.add_argument(
        "--smoothing", "-s",
        type=int,
        default=5,
        help="اندازه پنجره هموارسازی نمره‌ها"
    )
    
    args = parser.parse_args()
    
    # ایجاد instance تشخیص‌گر
    detector = TimerWakeWordDetector(
        chunk_size=args.chunk_size,
        threshold=args.threshold,
        device_id=args.device_id,
        cooldown_time=args.cooldown,
        smoothing_window=args.smoothing
    )
    
    # نمایش فهرست دستگاه‌ها در صورت درخواست
    if args.list_devices:
        detector.list_audio_devices()
        return
    
    # اجرای تشخیص‌گر
    success = detector.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()