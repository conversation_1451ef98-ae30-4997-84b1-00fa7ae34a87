#!/usr/bin/env python3
"""
تست اسکریپت team_bye_detector.py
این اسکریپت اطلاعات مدل را نمایش می‌دهد و یک تست سریع انجام می‌دهد
"""

import os
import sys
import numpy as np
from openwakeword.model import Model

def test_model_info():
    """نمایش اطلاعات مدل team_bye.tflite"""
    model_path = "/home/<USER>/Desktop/main/projects/openWakeWord/team_bye.tflite"
    
    print("=" * 60)
    print("🔍 بررسی مدل Team Bye")
    print("=" * 60)
    
    # بررسی وجود فایل
    if not os.path.exists(model_path):
        print(f"❌ خطا: فایل مدل یافت نشد: {model_path}")
        return False
    
    file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    print(f"📁 مسیر مدل: {model_path}")
    print(f"📏 حجم فایل: {file_size:.2f} MB")
    
    try:
        # بارگذاری مدل با openWakeWord
        model = Model(
            wakeword_models=[model_path],
            inference_framework='tflite'
        )
        
        # نمایش اطلاعات مدل
        available_models = list(model.models.keys())
        print(f"\n🔢 مدل‌های بارگذاری شده: {available_models}")
        
        # استخراج نام مدل
        filename = os.path.basename(model_path)
        model_name = filename.replace('.tflite', '')
        
        if model_name not in available_models and available_models:
            model_name = available_models[0]
        
        print(f"🎯 نام مدل فعال: {model_name}")
        
        # تست اجرای مدل با داده تصادفی
        print("\n🧪 تست اجرای مدل:")
        
        # ایجاد داده صوتی تصادفی (1280 نمونه = 80ms در 16kHz)
        test_audio = np.random.randint(-32768, 32767, 1280, dtype=np.int16)
        
        # پیش‌بینی
        predictions = model.predict(test_audio)
        
        if model_name in predictions:
            score = predictions[model_name]
            print(f"   ✅ تست موفق - نمره: {score:.4f}")
            print(f"   📊 تمام پیش‌بینی‌ها: {predictions}")
        else:
            print(f"   ⚠️  مدل '{model_name}' در پیش‌بینی‌ها یافت نشد")
            print(f"   📊 پیش‌بینی‌های موجود: {predictions}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا در بارگذاری مدل: {e}")
        return False

def test_audio_devices():
    """تست دستگاه‌های صوتی"""
    print("\n" + "=" * 60)
    print("🎤 بررسی دستگاه‌های صوتی")
    print("=" * 60)
    
    try:
        import pyaudio
        
        audio = pyaudio.PyAudio()
        input_devices = []
        
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                input_devices.append((i, info))
                print(f"🎧 ID {i}: {info['name']}")
                print(f"   کانال‌ها: {info['maxInputChannels']}")
                print(f"   نرخ نمونه‌برداری: {info['defaultSampleRate']} Hz")
                print()
        
        audio.terminate()
        
        if input_devices:
            print(f"✅ {len(input_devices)} دستگاه ورودی یافت شد")
        else:
            print("❌ هیچ دستگاه ورودی یافت نشد")
            
        return len(input_devices) > 0
        
    except ImportError:
        print("❌ PyAudio نصب نشده است")
        return False
    except Exception as e:
        print(f"❌ خطا در بررسی دستگاه‌های صوتی: {e}")
        return False

def main():
    print("🧪 تست اسکریپت Team Bye Detector")
    print()
    
    # تست مدل
    model_ok = test_model_info()
    
    # تست صوت
    audio_ok = test_audio_devices()
    
    # نتیجه نهایی
    print("=" * 60)
    print("📋 خلاصه تست‌ها:")
    print(f"   مدل: {'✅ آماده' if model_ok else '❌ مشکل دار'}")
    print(f"   صوت: {'✅ آماده' if audio_ok else '❌ مشکل دار'}")
    
    if model_ok and audio_ok:
        print("\n🎉 همه چیز آماده است!")
        print("برای اجرای تشخیص‌گر:")
        print("   python team_bye_detector.py")
        print("\nبرای تنظیم آستانه:")
        print("   python team_bye_detector.py --threshold 0.3")
        print("\nبرای انتخاب میکروفون:")
        print("   python team_bye_detector.py --device-id X")
    else:
        print("\n⚠️  برخی مشکلات برطرف شود قبل از اجرا")
        if not model_ok:
            print("   - بررسی کنید مدل team_bye.tflite در مسیر درست باشد")
        if not audio_ok:
            print("   - PyAudio را نصب کنید یا میکروفون را بررسی کنید")
    
    print("=" * 60)

if __name__ == "__main__":
    main()