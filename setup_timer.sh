#!/bin/bash

# اسکریپت راه‌اندازی سریع تشخیص Timer

echo "🚀 راه‌اندازی تشخیص دستورات Timer با openWakeWord"
echo "==============================================="

# رنگ‌ها برای خروجی
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # بدون رنگ

# بررسی محیط مجازی
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo -e "${GREEN}✅ محیط مجازی فعال است: $VIRTUAL_ENV${NC}"
else
    echo -e "${YELLOW}⚠️  محیط مجازی فعال نیست${NC}"
    echo "برای فعال‌سازی: source .venv/bin/activate"
    exit 1
fi

# نصب وابستگی‌ها
echo -e "\n${BLUE}📦 نصب وابستگی‌ها...${NC}"
pip install "numpy<2.0" --quiet

# بررسی نصب openwakeword
if python -c "import openwakeword" 2>/dev/null; then
    echo -e "${GREEN}✅ openwakeword نصب است${NC}"
else
    echo -e "${YELLOW}📦 نصب openwakeword...${NC}"
    pip install openwakeword --quiet
fi

# دانلود مدل timer
echo -e "\n${BLUE}⬇️  دانلود مدل timer...${NC}"
if python download_models.py; then
    echo -e "${GREEN}✅ مدل timer دانلود شد${NC}"
else
    echo -e "${RED}❌ خطا در دانلود مدل${NC}"
    exit 1
fi

# تست مدل
echo -e "\n${BLUE}🧪 تست مدل timer...${NC}"
if python test_timer.py; then
    echo -e "${GREEN}✅ مدل timer آماده است${NC}"
else
    echo -e "${RED}❌ خطا در تست مدل${NC}"
    exit 1
fi

# نمایش دستگاه‌های صوتی
echo -e "\n${BLUE}🎤 دستگاه‌های صوتی موجود:${NC}"
python timer_detector.py --list-devices

echo -e "\n${GREEN}🎉 راه‌اندازی کامل شد!${NC}"
echo -e "\n${YELLOW}📋 دستورات مفید:${NC}"
echo "   # اجرای عادی"
echo "   python timer_detector.py"
echo ""
echo "   # حساسیت بالا"
echo "   python timer_detector.py --threshold 0.2"
echo ""
echo "   # محیط پرسروصدا"
echo "   python timer_detector.py --threshold 0.5 --smoothing 7"
echo ""
echo "   # با دستگاه مشخص (PulseAudio)"
echo "   python timer_detector.py --device-id 13"
echo ""

echo -e "${BLUE}🗣️  عبارات نمونه برای تست:${NC}"
echo "   • 'set a timer for 5 minutes'"
echo "   • 'start 10 minute timer'"
echo "   • 'set an alarm for 1 hour'"
echo ""

read -p "آیا می‌خواهید الان برنامه را اجرا کنید؟ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}🚀 اجرای برنامه تشخیص timer...${NC}"
    python timer_detector.py --threshold 0.3
fi