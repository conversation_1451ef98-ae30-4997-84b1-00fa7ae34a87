#!/usr/bin/env python3
"""
اسکریپت کمکی برای دانلود مدل‌های openWakeWord
"""

import argparse
import logging
from openwakeword.utils import download_models

def main():
    parser = argparse.ArgumentParser(
        description="دانلود مدل‌های openWakeWord",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--models", "-m",
        nargs="+",
        default=[],
        help="نام‌های مدل‌های مورد نیاز برای دانلود (alexa, hey_jarvis, hey_mycroft, timer, weather)"
    )
    
    args = parser.parse_args()
    
    # تنظیم logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("شروع دانلود مدل‌ها...")
        if args.models:
            logger.info(f"دانلود مدل‌های: {args.models}")
            download_models(model_names=["timer"])
        else:
            logger.info("دانلود همه مدل‌های موجود...")
            download_models(model_names=["timer"])
        
        logger.info("دانلود مدل‌ها با موفقیت کامل شد!")
        
    except Exception as e:
        logger.error(f"خطا در دانلود مدل‌ها: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())