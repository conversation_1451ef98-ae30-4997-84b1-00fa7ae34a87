#!/usr/bin/env python3
"""
تست مدل timer بدون نیاز به صحبت
"""

import numpy as np
import time
from openwakeword.model import Model

def test_timer_model():
    """تست مدل timer با سیگنال ساختگی"""
    print("🧪 شروع تست مدل timer...")
    
    try:
        # بارگذاری مدل
        model = Model(inference_framework='tflite')
        
        if 'timer' not in model.models:
            print("❌ مدل timer یافت نشد")
            return
            
        print("✅ مدل timer بارگذاری شد")
        
        # تولید سیگنال تست
        sample_rate = 16000
        chunk_size = 1280  # 80ms
        
        # شبیه‌سازی 10 فریم صوت
        print("\n📊 تست با فریم‌های صوتی...")
        
        for i in range(10):
            # تولید سیگنال تصادفی (شبیه‌سازی صدا)
            audio_frame = np.random.randint(-1000, 1000, chunk_size, dtype=np.int16)
            
            # پیش‌بینی
            predictions = model.predict(audio_frame)
            
            # نمایش نتایج timer
            if 'timer' in predictions:
                timer_prediction_buffer = model.prediction_buffer.get('timer', {})
                if timer_prediction_buffer:
                    latest_scores = {k: (v[-1] if v else 0.0) for k, v in timer_prediction_buffer.items()}
                    
                    # پیدا کردن بالاترین نمره
                    if latest_scores:
                        best_class = max(latest_scores, key=latest_scores.get)
                        best_score = latest_scores[best_class]
                        
                        print(f"   فریم {i+1:2d}: بهترین کلاس = {best_class:15s}, نمره = {best_score:.4f}")
                        
                        if best_score > 0.1:  # نمره معنی‌دار
                            print(f"      🎯 تشخیص محتمل: {best_class}")
            
            time.sleep(0.08)  # تأخیر 80ms
        
        print("\n✅ تست تکمیل شد!")
        print("\n📋 کلاس‌های timer قابل تشخیص:")
        print("   • 1_minute_timer  - تایمر 1 دقیقه")
        print("   • 5_minute_timer  - تایمر 5 دقیقه")
        print("   • 10_minute_timer - تایمر 10 دقیقه")
        print("   • 20_minute_timer - تایمر 20 دقیقه") 
        print("   • 30_minute_timer - تایمر 30 دقیقه")
        print("   • 1_hour_timer    - تایمر 1 ساعت")
        
    except Exception as e:
        print(f"❌ خطا در تست: {e}")

if __name__ == "__main__":
    test_timer_model()